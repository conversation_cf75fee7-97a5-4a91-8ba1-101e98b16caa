using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace YouTubeIDM
{
    public partial class MainForm : Form
    {
        private YouTubeDownloader _downloader = null!;
        private VideoInfo? _currentVideoInfo;
        private AppSettings _settings = null!;
        private string _outputDirectory = null!;

        public MainForm()
        {
            InitializeComponent();
            LoadSettings();
            InitializeDownloader();
            SetupUI();
        }

        private void LoadSettings()
        {
            _settings = AppSettings.Load();
            _settings.Validate();
            _outputDirectory = _settings.DefaultDownloadPath;

            // تطبيق إعدادات النافذة
            if (_settings.RememberWindowSize)
            {
                this.Size = new System.Drawing.Size(_settings.WindowWidth, _settings.WindowHeight);
                if (_settings.WindowX >= 0 && _settings.WindowY >= 0)
                {
                    this.StartPosition = FormStartPosition.Manual;
                    this.Location = new System.Drawing.Point(_settings.WindowX, _settings.WindowY);
                }
            }
        }

        private void InitializeDownloader()
        {
            _downloader = new YouTubeDownloader();
            _downloader.ProgressChanged += OnDownloadProgressChanged;
            _downloader.DownloadCompleted += OnDownloadCompleted;
            _downloader.StatusChanged += OnStatusChanged;
        }

        private void SetupUI()
        {
            // إعداد المجلد الافتراضي
            lblOutputPath.Text = _outputDirectory;

            // إعداد شريط الحالة
            statusLabel.Text = "جاهز";

            // تعطيل أزرار التحميل في البداية
            btnDownload.Enabled = false;
            btnCancel.Enabled = false;
        }

        private void btnPaste_Click(object sender, EventArgs e)
        {
            try
            {
                if (Clipboard.ContainsText())
                {
                    txtUrl.Text = Clipboard.GetText();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في لصق النص: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void btnFetchInfo_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtUrl.Text))
            {
                MessageBox.Show("يرجى إدخال رابط YouTube صحيح", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnFetchInfo.Enabled = false;
                btnFetchInfo.Text = "جاري التحليل...";

                _currentVideoInfo = await _downloader.GetVideoInfoAsync(txtUrl.Text.Trim());

                if (_currentVideoInfo != null)
                {
                    DisplayVideoInfo(_currentVideoInfo);
                    btnDownload.Enabled = true;
                }
                else
                {
                    MessageBox.Show("فشل في تحليل الرابط. يرجى التأكد من صحة الرابط.", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحليل الرابط: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnFetchInfo.Enabled = true;
                btnFetchInfo.Text = "تحليل الرابط";
            }
        }

        private void DisplayVideoInfo(VideoInfo videoInfo)
        {
            // عرض معلومات الفيديو
            if (videoInfo.IsPlaylist)
            {
                lblVideoTitle.Text = $"قائمة تشغيل: {videoInfo.Title} ({videoInfo.Videos.Count} فيديو)";

                // ملء قائمة الفيديوهات
                listVideos.Items.Clear();
                foreach (var video in videoInfo.Videos)
                {
                    var item = new ListViewItem(video.Title);
                    item.SubItems.Add(video.Author);
                    item.SubItems.Add(video.GetFormattedDuration());
                    item.Tag = video;
                    item.Checked = true; // تحديد جميع الفيديوهات افتراضياً
                    listVideos.Items.Add(item);
                }

                listVideos.Visible = true;
                lblVideoTitle.Text += $"\nاختر الفيديوهات المراد تحميلها:";
            }
            else
            {
                var video = videoInfo.Videos.First();
                lblVideoTitle.Text = $"العنوان: {video.Title}\nالمؤلف: {video.Author}\nالمدة: {video.GetFormattedDuration()}";
                listVideos.Visible = false;
            }

            // ملء قوائم الجودة والصيغة
            cmbQuality.Items.Clear();
            cmbQuality.Items.AddRange([.. videoInfo.AvailableQualities]);
            if (cmbQuality.Items.Count > 0)
                cmbQuality.SelectedIndex = 0;

            cmbFormat.Items.Clear();
            cmbFormat.Items.AddRange([.. videoInfo.AvailableFormats]);
            if (cmbFormat.Items.Count > 0)
                cmbFormat.SelectedIndex = 0;
        }

        private async void btnDownload_Click(object sender, EventArgs e)
        {
            if (_currentVideoInfo == null || cmbQuality.SelectedItem == null)
            {
                MessageBox.Show("يرجى تحليل الرابط واختيار الجودة أولاً", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnDownload.Enabled = false;
                btnCancel.Enabled = true;
                btnFetchInfo.Enabled = false;

                var selectedQuality = cmbQuality.SelectedItem.ToString()!;
                var selectedFormat = cmbFormat.SelectedItem?.ToString() ?? "";

                if (_currentVideoInfo.IsPlaylist)
                {
                    // تحميل الفيديوهات المحددة من القائمة
                    var selectedVideos = listVideos.CheckedItems.Cast<ListViewItem>()
                        .Select(item => (DownloadItem)item.Tag!)
                        .ToList();

                    if (selectedVideos.Count == 0)
                    {
                        MessageBox.Show("يرجى اختيار فيديو واحد على الأقل للتحميل", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    await DownloadMultipleVideos(selectedVideos, selectedQuality, selectedFormat);
                }
                else
                {
                    // تحميل فيديو واحد
                    var video = _currentVideoInfo.Videos.First();
                    await _downloader.DownloadVideoAsync(video, _outputDirectory, selectedQuality, selectedFormat);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحميل: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnDownload.Enabled = true;
                btnCancel.Enabled = false;
                btnFetchInfo.Enabled = true;
            }
        }

        private async Task DownloadMultipleVideos(List<DownloadItem> videos, string quality, string format)
        {
            var totalVideos = videos.Count;
            var currentVideo = 0;

            foreach (var video in videos)
            {
                currentVideo++;
                statusLabel.Text = $"تحميل الفيديو {currentVideo} من {totalVideos}: {video.Title}";

                var success = await _downloader.DownloadVideoAsync(video, _outputDirectory, quality, format);

                if (!success)
                {
                    var result = MessageBox.Show(
                        $"فشل في تحميل الفيديو: {video.Title}\nهل تريد المتابعة مع الفيديوهات الأخرى؟",
                        "خطأ في التحميل",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                        break;
                }
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            _downloader.CancelDownload();
            btnCancel.Enabled = false;
        }

        private void btnBrowse_Click(object sender, EventArgs e)
        {
            using var dialog = new FolderBrowserDialog();
            dialog.Description = "اختر مجلد التحميل";
            dialog.SelectedPath = _outputDirectory;

            if (dialog.ShowDialog() == DialogResult.OK)
            {
                _outputDirectory = dialog.SelectedPath;
                lblOutputPath.Text = _outputDirectory;
            }
        }

        private void OnDownloadProgressChanged(object? sender, DownloadProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDownloadProgressChanged(sender, e)));
                return;
            }

            var item = e.Item;
            progressBar.Value = (int)Math.Min(item.ProgressPercentage, 100);

            lblProgress.Text = $"{item.ProgressPercentage:F1}% - {item.GetFormattedDownloadedSize()} / {item.GetFormattedFileSize()}";
            lblSpeed.Text = $"السرعة: {item.GetFormattedSpeed()}";

            if (item.EstimatedTimeRemaining != TimeSpan.Zero)
            {
                lblTimeRemaining.Text = $"الوقت المتبقي: {item.EstimatedTimeRemaining:mm\\:ss}";
            }
        }

        private void OnDownloadCompleted(object? sender, DownloadCompletedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnDownloadCompleted(sender, e)));
                return;
            }

            if (e.Success)
            {
                progressBar.Value = 100;
                lblProgress.Text = "اكتمل التحميل!";
                MessageBox.Show($"تم تحميل الفيديو بنجاح:\n{e.Item.Title}", "تم التحميل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show($"فشل في تحميل الفيديو:\n{e.ErrorMessage}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OnStatusChanged(object? sender, string status)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnStatusChanged(sender, status)));
                return;
            }

            statusLabel.Text = status;
        }

        private void SaveSettings()
        {
            if (_settings.RememberWindowSize)
            {
                _settings.WindowWidth = this.Size.Width;
                _settings.WindowHeight = this.Size.Height;
                _settings.WindowX = this.Location.X;
                _settings.WindowY = this.Location.Y;
            }

            _settings.DefaultDownloadPath = _outputDirectory;
            _settings.Save();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            SaveSettings();
            _downloader?.Dispose();
            base.OnFormClosed(e);
        }
    }
}
