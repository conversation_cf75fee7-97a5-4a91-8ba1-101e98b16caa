using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace YouTubeIDM
{
    /// <summary>
    /// فئة مساعدة للتعامل مع FFmpeg لدمج الفيديو والصوت
    /// </summary>
    public static class FFmpegHelper
    {
        private static string? _ffmpegPath;

        /// <summary>
        /// التحقق من وجود FFmpeg في النظام
        /// </summary>
        public static bool IsFFmpegAvailable()
        {
            if (_ffmpegPath != null)
                return true;

            // البحث عن FFmpeg في المسارات المختلفة
            var possiblePaths = new[]
            {
                "ffmpeg.exe",
                "ffmpeg",
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ffmpeg.exe"),
                Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tools", "ffmpeg.exe"),
                @"C:\ffmpeg\bin\ffmpeg.exe",
                @"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
                @"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe"
            };

            foreach (var path in possiblePaths)
            {
                if (IsFFmpegExecutable(path))
                {
                    _ffmpegPath = path;
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// التحقق من أن الملف هو FFmpeg صالح
        /// </summary>
        private static bool IsFFmpegExecutable(string path)
        {
            try
            {
                if (!File.Exists(path))
                    return false;

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = path,
                        Arguments = "-version",
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                return output.Contains("ffmpeg version") && process.ExitCode == 0;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// دمج ملفات الفيديو والصوت باستخدام FFmpeg
        /// </summary>
        public static async Task<bool> MergeVideoAndAudioAsync(string videoPath, string audioPath, string outputPath, IProgress<string>? progress = null)
        {
            if (!IsFFmpegAvailable())
            {
                throw new InvalidOperationException("FFmpeg غير متوفر. يرجى تثبيت FFmpeg أولاً.");
            }

            try
            {
                progress?.Report("بدء دمج الفيديو والصوت...");

                var arguments = $"-i \"{videoPath}\" -i \"{audioPath}\" -c copy -map 0:v:0 -map 1:a:0 \"{outputPath}\"";

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = _ffmpegPath!,
                        Arguments = arguments,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();

                // قراءة المخرجات بشكل غير متزامن
                var outputTask = process.StandardOutput.ReadToEndAsync();
                var errorTask = process.StandardError.ReadToEndAsync();

                await process.WaitForExitAsync();

                var output = await outputTask;
                var error = await errorTask;

                if (process.ExitCode == 0)
                {
                    progress?.Report("تم دمج الفيديو والصوت بنجاح");
                    return true;
                }
                else
                {
                    var errorMessage = $"فشل في دمج الفيديو والصوت. رمز الخطأ: {process.ExitCode}\nالخطأ: {error}";
                    throw new Exception(errorMessage);
                }
            }
            catch (Exception ex)
            {
                progress?.Report($"خطأ في دمج الفيديو والصوت: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تحويل ملف صوتي إلى صيغة أخرى
        /// </summary>
        public static async Task<bool> ConvertAudioAsync(string inputPath, string outputPath, string format, IProgress<string>? progress = null)
        {
            if (!IsFFmpegAvailable())
            {
                throw new InvalidOperationException("FFmpeg غير متوفر. يرجى تثبيت FFmpeg أولاً.");
            }

            try
            {
                progress?.Report($"تحويل الصوت إلى صيغة {format}...");

                var arguments = $"-i \"{inputPath}\" -acodec {GetAudioCodec(format)} \"{outputPath}\"";

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = _ffmpegPath!,
                        Arguments = arguments,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                await process.WaitForExitAsync();

                if (process.ExitCode == 0)
                {
                    progress?.Report("تم تحويل الصوت بنجاح");
                    return true;
                }
                else
                {
                    throw new Exception($"فشل في تحويل الصوت. رمز الخطأ: {process.ExitCode}");
                }
            }
            catch (Exception ex)
            {
                progress?.Report($"خطأ في تحويل الصوت: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// الحصول على codec الصوت المناسب للصيغة
        /// </summary>
        private static string GetAudioCodec(string format)
        {
            return format.ToLower() switch
            {
                "mp3" => "libmp3lame",
                "aac" => "aac",
                "m4a" => "aac",
                "ogg" => "libvorbis",
                "flac" => "flac",
                "wav" => "pcm_s16le",
                _ => "copy"
            };
        }

        /// <summary>
        /// الحصول على معلومات حول ملف الوسائط
        /// </summary>
        public static async Task<string> GetMediaInfoAsync(string filePath)
        {
            if (!IsFFmpegAvailable())
            {
                throw new InvalidOperationException("FFmpeg غير متوفر. يرجى تثبيت FFmpeg أولاً.");
            }

            try
            {
                var arguments = $"-i \"{filePath}\"";

                var process = new Process
                {
                    StartInfo = new ProcessStartInfo
                    {
                        FileName = _ffmpegPath!,
                        Arguments = arguments,
                        UseShellExecute = false,
                        RedirectStandardOutput = true,
                        RedirectStandardError = true,
                        CreateNoWindow = true
                    }
                };

                process.Start();
                var error = await process.StandardError.ReadToEndAsync();
                await process.WaitForExitAsync();

                return error; // FFmpeg يرسل معلومات الملف إلى stderr
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في الحصول على معلومات الملف: {ex.Message}");
            }
        }

        /// <summary>
        /// تنزيل FFmpeg تلقائياً (اختياري)
        /// </summary>
        public static string GetFFmpegDownloadUrl()
        {
            return "https://github.com/BtbN/FFmpeg-Builds/releases/latest/download/ffmpeg-master-latest-win64-gpl.zip";
        }

        /// <summary>
        /// رسالة تعليمات تثبيت FFmpeg
        /// </summary>
        public static string GetInstallationInstructions()
        {
            return @"لاستخدام ميزة دمج الفيديو والصوت، يرجى تثبيت FFmpeg:

1. قم بتنزيل FFmpeg من: https://ffmpeg.org/download.html
2. استخرج الملفات إلى مجلد (مثل C:\ffmpeg)
3. أضف مجلد bin إلى متغير البيئة PATH
4. أو ضع ffmpeg.exe في نفس مجلد التطبيق

بدلاً من ذلك، يمكنك وضع ffmpeg.exe في:
- نفس مجلد التطبيق
- مجلد tools داخل مجلد التطبيق
- C:\ffmpeg\bin\
- C:\Program Files\ffmpeg\bin\";
        }
    }
}
