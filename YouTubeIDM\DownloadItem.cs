using System;

namespace YouTubeIDM
{
    /// <summary>
    /// يمثل عنصر تحميل واحد مع معلوماته وحالته
    /// </summary>
    public class DownloadItem
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public string ThumbnailUrl { get; set; } = string.Empty;
        public TimeSpan Duration { get; set; }
        public string Author { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public long DownloadedBytes { get; set; }
        public string OutputPath { get; set; } = string.Empty;
        public string SelectedQuality { get; set; } = string.Empty;
        public string SelectedFormat { get; set; } = string.Empty;
        public DownloadStatus Status { get; set; } = DownloadStatus.Pending;
        public double ProgressPercentage => FileSize > 0 ? (double)DownloadedBytes / FileSize * 100 : 0;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        
        // معلومات السرعة
        public double DownloadSpeed { get; set; } // بايت في الثانية
        public TimeSpan EstimatedTimeRemaining { get; set; }
        
        public string GetFormattedFileSize()
        {
            return FormatBytes(FileSize);
        }
        
        public string GetFormattedDownloadedSize()
        {
            return FormatBytes(DownloadedBytes);
        }
        
        public string GetFormattedSpeed()
        {
            return $"{FormatBytes((long)DownloadSpeed)}/s";
        }
        
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
        
        public string GetFormattedDuration()
        {
            if (Duration.TotalHours >= 1)
                return Duration.ToString(@"h\:mm\:ss");
            else
                return Duration.ToString(@"m\:ss");
        }
    }
    
    public enum DownloadStatus
    {
        Pending,      // في الانتظار
        Downloading,  // جاري التحميل
        Paused,       // متوقف مؤقتاً
        Completed,    // مكتمل
        Failed,       // فشل
        Cancelled     // ملغي
    }
}
