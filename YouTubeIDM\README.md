# YouTube IDM - مدير تحميل اليوتيوب

تطبيق Windows Forms مطور بـ C# لتحميل الفيديوهات والصوتيات من YouTube بجودات مختلفة.

## المميزات

### الوظائف الأساسية
- **تحليل روابط YouTube**: دعم روابط الفيديوهات المفردة وقوائم التشغيل
- **معاينة معلومات الفيديو**: عرض العنوان، المؤلف، المدة، والصورة المصغرة
- **اختيار الجودة**: خيارات متعددة للجودة (1080p, 720p, 480p, إلخ)
- **صيغ متنوعة**: دعم تحميل الفيديو والصوت بصيغ مختلفة
- **تحميل قوائم التشغيل**: إمكانية تحميل فيديوهات متعددة من قائمة واحدة
- **دمج الفيديو والصوت**: دعم تحميل فيديو عالي الجودة مع دمج الصوت (يتطلب FFmpeg)

### واجهة المستخدم
- **واجهة عربية**: تصميم يدعم اللغة العربية مع RTL
- **تقدم التحميل**: شريط تقدم مع معلومات السرعة والوقت المتبقي
- **إدارة التحميلات**: إمكانية إلغاء التحميل أثناء العملية
- **اختيار مجلد الحفظ**: تحديد مكان حفظ الملفات

## المتطلبات

- .NET 9.0 أو أحدث
- Windows 10/11
- اتصال بالإنترنت
- FFmpeg (اختياري - لدمج الفيديو والصوت عالي الجودة)

## المكتبات المستخدمة

- **YoutubeExplode**: للتفاعل مع YouTube API
- **Newtonsoft.Json**: لمعالجة JSON
- **System.Net.Http**: لتحميل الملفات

## التثبيت والتشغيل

### من الكود المصدري

1. استنساخ المشروع:
```bash
git clone [repository-url]
cd YouTubeIDM
```

2. استعادة المكتبات:
```bash
dotnet restore
```

3. بناء المشروع:
```bash
dotnet build
```

4. تشغيل التطبيق:
```bash
dotnet run
```

### إنشاء ملف تنفيذي

```bash
dotnet publish -c Release -r win-x64 --self-contained
```

### تثبيت FFmpeg (اختياري)

لاستخدام ميزة دمج الفيديو والصوت عالي الجودة:

1. **تنزيل FFmpeg**:
   - اذهب إلى [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
   - اختر "Windows" ثم "Windows builds by BtbN"
   - حمل النسخة الأحدث (ffmpeg-master-latest-win64-gpl.zip)

2. **التثبيت**:
   ```bash
   # استخرج الملفات إلى مجلد
   C:\ffmpeg\

   # أضف المجلد إلى PATH
   C:\ffmpeg\bin
   ```

3. **طرق بديلة**:
   - ضع `ffmpeg.exe` في نفس مجلد التطبيق
   - ضع `ffmpeg.exe` في مجلد `tools` داخل مجلد التطبيق

4. **التحقق من التثبيت**:
   ```bash
   ffmpeg -version
   ```

## طريقة الاستخدام

### تحميل فيديو واحد

1. **إدخال الرابط**: الصق رابط YouTube في حقل النص
2. **تحليل الرابط**: اضغط على "تحليل الرابط" لاستخراج معلومات الفيديو
3. **اختيار الجودة**: حدد الجودة والصيغة المطلوبة
4. **اختيار مجلد الحفظ**: حدد مكان حفظ الملف (اختياري)
5. **بدء التحميل**: اضغط على "تحميل"

### تحميل قائمة تشغيل

1. **إدخال رابط القائمة**: الصق رابط قائمة التشغيل
2. **تحليل القائمة**: اضغط على "تحليل الرابط"
3. **اختيار الفيديوهات**: حدد الفيديوهات المراد تحميلها من القائمة
4. **إعداد التحميل**: اختر الجودة والصيغة
5. **بدء التحميل**: اضغط على "تحميل"

## هيكل المشروع

```
YouTubeIDM/
├── Program.cs              # نقطة دخول التطبيق
├── Form1.cs               # النموذج الرئيسي (MainForm)
├── Form1.Designer.cs      # تصميم واجهة المستخدم
├── YouTubeDownloader.cs   # فئة إدارة التحميل
├── DownloadItem.cs        # فئة عنصر التحميل
├── YouTubeIDM.csproj     # ملف المشروع
└── README.md             # هذا الملف
```

## الفئات الرئيسية

### `MainForm`
- إدارة واجهة المستخدم
- معالجة أحداث الأزرار
- عرض تقدم التحميل

### `YouTubeDownloader`
- تحليل روابط YouTube
- استخراج معلومات الفيديو
- إدارة عملية التحميل
- معالجة الأخطاء

### `DownloadItem`
- تمثيل عنصر تحميل واحد
- تخزين معلومات الفيديو
- تتبع حالة التحميل

## المميزات المتقدمة

- **تحميل غير متزامن**: عدم تجميد واجهة المستخدم أثناء التحميل
- **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة
- **إلغاء التحميل**: إمكانية إيقاف التحميل في أي وقت
- **تنسيق أسماء الملفات**: تنظيف أسماء الملفات من الرموز غير المدعومة

## الأخطاء الشائعة وحلولها

### "رابط غير صحيح"
- تأكد من أن الرابط من YouTube
- تحقق من صحة تنسيق الرابط

### "فشل في التحميل"
- تحقق من اتصال الإنترنت
- تأكد من توفر مساحة كافية على القرص
- جرب جودة أقل

### "لم يتم العثور على التدفق المطلوب"
- جرب جودة مختلفة
- تحقق من توفر الجودة المطلوبة للفيديو

## التطوير المستقبلي

- [ ] دعم تحميل متعدد الخيوط
- [ ] إضافة ميزة الإيقاف والاستكمال
- [ ] دعم مواقع أخرى غير YouTube
- [ ] إضافة قائمة انتظار للتحميلات
- [ ] حفظ إعدادات المستخدم
- [ ] دعم تحميل الترجمات

## المساهمة

نرحب بالمساهمات! يرجى:

1. إنشاء Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إجراء التغييرات المطلوبة
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف LICENSE للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى:
- فتح Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**ملاحظة**: هذا التطبيق مخصص للاستخدام الشخصي فقط. يرجى احترام حقوق الطبع والنشر وشروط استخدام YouTube.
