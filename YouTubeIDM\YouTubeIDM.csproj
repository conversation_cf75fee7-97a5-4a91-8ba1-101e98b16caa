﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- Application Information -->
    <AssemblyTitle>YouTube IDM</AssemblyTitle>
    <AssemblyDescription>مدير تحميل اليوتيوب - YouTube Download Manager</AssemblyDescription>
    <AssemblyCompany>YouTube IDM</AssemblyCompany>
    <AssemblyProduct>YouTube IDM</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>

    <!-- Build Configuration -->
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <StartupObject>YouTubeIDM.Program</StartupObject>

    <!-- Publishing Options -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <PublishTrimmed>false</PublishTrimmed>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="YoutubeExplode" Version="6.5.4" />
  </ItemGroup>

  <!-- Include additional files -->
  <ItemGroup>
    <None Include="README.md" />
    <None Include="USAGE.md" />
    <None Include=".gitignore" />
  </ItemGroup>

</Project>