using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using YoutubeExplode;
using YoutubeExplode.Videos;
using YoutubeExplode.Videos.Streams;
using YoutubeExplode.Playlists;
using YoutubeExplode.Common;

namespace YouTubeIDM
{
    /// <summary>
    /// فئة للتعامل مع عمليات تحميل الفيديوهات من YouTube
    /// </summary>
    public class YouTubeDownloader
    {
        private readonly YoutubeClient _youtube;
        private readonly HttpClient _httpClient;
        private CancellationTokenSource? _cancellationTokenSource;

        public event EventHandler<DownloadProgressEventArgs>? ProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;
        public event EventHandler<string>? StatusChanged;

        public YouTubeDownloader()
        {
            _youtube = new YoutubeClient();
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(30); // مهلة زمنية طويلة للتحميلات الكبيرة
        }

        /// <summary>
        /// تحليل URL واستخراج معلومات الفيديو أو القائمة
        /// </summary>
        public async Task<VideoInfo?> GetVideoInfoAsync(string url)
        {
            try
            {
                StatusChanged?.Invoke(this, "جاري تحليل الرابط...");

                // التحقق من نوع الرابط
                if (PlaylistId.TryParse(url) != null)
                {
                    // هذا رابط قائمة تشغيل
                    return await GetPlaylistInfoAsync(url);
                }
                else if (VideoId.TryParse(url) != null)
                {
                    // هذا رابط فيديو واحد
                    return await GetSingleVideoInfoAsync(url);
                }
                else
                {
                    throw new ArgumentException("رابط غير صحيح. يرجى التأكد من أن الرابط من YouTube.");
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"خطأ في تحليل الرابط: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo> GetSingleVideoInfoAsync(string url)
        {
            var video = await _youtube.Videos.GetAsync(url);
            var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(video.Id);

            var videoInfo = new VideoInfo
            {
                Title = video.Title,
                IsPlaylist = false,
                Videos = new List<DownloadItem>()
            };

            var downloadItem = new DownloadItem
            {
                Id = video.Id,
                Title = video.Title,
                Url = url,
                ThumbnailUrl = video.Thumbnails.GetWithHighestResolution()?.Url ?? "",
                Duration = video.Duration ?? TimeSpan.Zero,
                Author = video.Author.ChannelTitle
            };

            videoInfo.Videos.Add(downloadItem);
            videoInfo.AvailableQualities = GetAvailableQualities(streamManifest);
            videoInfo.AvailableFormats = GetAvailableFormats(streamManifest);

            return videoInfo;
        }

        private async Task<VideoInfo> GetPlaylistInfoAsync(string url)
        {
            var playlist = await _youtube.Playlists.GetAsync(url);
            var videos = await _youtube.Playlists.GetVideosAsync(playlist.Id);

            var videoInfo = new VideoInfo
            {
                Title = playlist.Title,
                IsPlaylist = true,
                Videos = new List<DownloadItem>()
            };

            foreach (var video in videos)
            {
                var downloadItem = new DownloadItem
                {
                    Id = video.Id,
                    Title = video.Title,
                    Url = $"https://www.youtube.com/watch?v={video.Id}",
                    ThumbnailUrl = video.Thumbnails.GetWithHighestResolution()?.Url ?? "",
                    Duration = video.Duration ?? TimeSpan.Zero,
                    Author = video.Author.ChannelTitle
                };

                videoInfo.Videos.Add(downloadItem);
            }

            // الحصول على الجودات المتاحة من أول فيديو
            if (videoInfo.Videos.Any())
            {
                var firstVideo = videoInfo.Videos.First();
                var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(firstVideo.Id);
                videoInfo.AvailableQualities = GetAvailableQualities(streamManifest);
                videoInfo.AvailableFormats = GetAvailableFormats(streamManifest);
            }

            return videoInfo;
        }

        private List<string> GetAvailableQualities(StreamManifest streamManifest)
        {
            var qualities = new List<string>();

            // إضافة الجودات المدمجة (فيديو + صوت)
            var muxedStreams = streamManifest.GetMuxedStreams().OrderByDescending(s => s.VideoQuality.MaxHeight);
            foreach (var stream in muxedStreams)
            {
                qualities.Add($"{stream.VideoQuality.Label} (مدمج)");
            }

            // إضافة جودات الفيديو عالية الجودة مع دمج الصوت
            var videoStreams = streamManifest.GetVideoOnlyStreams().OrderByDescending(s => s.VideoQuality.MaxHeight);
            var hasAudioStreams = streamManifest.GetAudioOnlyStreams().Any();

            foreach (var stream in videoStreams)
            {
                // إضافة خيار الفيديو فقط
                qualities.Add($"{stream.VideoQuality.Label} (فيديو فقط)");

                // إضافة خيار الفيديو مع دمج الصوت إذا كان الصوت متوفراً
                if (hasAudioStreams)
                {
                    qualities.Add($"{stream.VideoQuality.Label} (فيديو + صوت)");
                }
            }

            // إضافة خيار الصوت فقط
            if (hasAudioStreams)
            {
                qualities.Add("صوت فقط");
            }

            return qualities.Distinct().ToList();
        }

        private List<string> GetAvailableFormats(StreamManifest streamManifest)
        {
            var formats = new List<string>();

            // إضافة صيغ الصوت
            var audioStreams = streamManifest.GetAudioOnlyStreams();
            foreach (var stream in audioStreams)
            {
                var format = $"{stream.Container.Name.ToUpper()} ({stream.Bitrate})";
                if (!formats.Contains(format))
                    formats.Add(format);
            }

            return formats;
        }

        /// <summary>
        /// بدء تحميل فيديو
        /// </summary>
        public async Task<bool> DownloadVideoAsync(DownloadItem item, string outputDirectory, string quality, string format)
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                item.Status = DownloadStatus.Downloading;
                item.StartTime = DateTime.Now;

                StatusChanged?.Invoke(this, $"جاري تحميل: {item.Title}");

                var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(item.Id);

                // اختيار التدفق المناسب حسب الجودة المحددة
                if (quality.Contains("صوت فقط"))
                {
                    return await DownloadAudioOnlyAsync(item, outputDirectory, format, streamManifest);
                }
                else if (quality.Contains("مدمج"))
                {
                    return await DownloadMuxedVideoAsync(item, outputDirectory, quality, streamManifest);
                }
                else if (quality.Contains("فيديو فقط"))
                {
                    return await DownloadVideoOnlyAsync(item, outputDirectory, quality, streamManifest);
                }
                else if (quality.Contains("فيديو + صوت"))
                {
                    return await DownloadVideoWithAudioAsync(item, outputDirectory, quality, streamManifest);
                }

                throw new Exception("نوع الجودة غير مدعوم");
            }
            catch (OperationCanceledException)
            {
                item.Status = DownloadStatus.Cancelled;
                StatusChanged?.Invoke(this, "تم إلغاء التحميل");
                return false;
            }
            catch (Exception ex)
            {
                item.Status = DownloadStatus.Failed;
                item.ErrorMessage = ex.Message;
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, false, ex.Message));
                StatusChanged?.Invoke(this, $"فشل التحميل: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل صوت فقط
        /// </summary>
        private async Task<bool> DownloadAudioOnlyAsync(DownloadItem item, string outputDirectory, string format, StreamManifest streamManifest)
        {
            var selectedStream = streamManifest.GetAudioOnlyStreams()
                .Where(s => format.Contains(s.Container.Name.ToUpper()))
                .OrderByDescending(s => s.Bitrate)
                .FirstOrDefault();

            if (selectedStream == null)
            {
                throw new Exception("لم يتم العثور على تدفق الصوت المطلوب");
            }

            var fileName = $"{SanitizeFileName(item.Title)}.{selectedStream.Container.Name}";
            var filePath = Path.Combine(outputDirectory, fileName);
            item.OutputPath = filePath;

            await DownloadStreamAsync(selectedStream, filePath, item, _cancellationTokenSource!.Token);

            item.Status = DownloadStatus.Completed;
            item.EndTime = DateTime.Now;
            DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, true, null));
            return true;
        }

        /// <summary>
        /// تحميل فيديو مدمج (فيديو + صوت في ملف واحد)
        /// </summary>
        private async Task<bool> DownloadMuxedVideoAsync(DownloadItem item, string outputDirectory, string quality, StreamManifest streamManifest)
        {
            var qualityLabel = quality.Replace(" (مدمج)", "");
            var selectedStream = streamManifest.GetMuxedStreams()
                .FirstOrDefault(s => s.VideoQuality.Label == qualityLabel);

            if (selectedStream == null)
            {
                throw new Exception("لم يتم العثور على التدفق المدمج المطلوب");
            }

            var fileName = $"{SanitizeFileName(item.Title)}.{selectedStream.Container.Name}";
            var filePath = Path.Combine(outputDirectory, fileName);
            item.OutputPath = filePath;

            await DownloadStreamAsync(selectedStream, filePath, item, _cancellationTokenSource!.Token);

            item.Status = DownloadStatus.Completed;
            item.EndTime = DateTime.Now;
            DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, true, null));
            return true;
        }

        /// <summary>
        /// تحميل فيديو فقط (بدون صوت)
        /// </summary>
        private async Task<bool> DownloadVideoOnlyAsync(DownloadItem item, string outputDirectory, string quality, StreamManifest streamManifest)
        {
            var qualityLabel = quality.Replace(" (فيديو فقط)", "");
            var selectedStream = streamManifest.GetVideoOnlyStreams()
                .FirstOrDefault(s => s.VideoQuality.Label == qualityLabel);

            if (selectedStream == null)
            {
                throw new Exception("لم يتم العثور على تدفق الفيديو المطلوب");
            }

            var fileName = $"{SanitizeFileName(item.Title)}.{selectedStream.Container.Name}";
            var filePath = Path.Combine(outputDirectory, fileName);
            item.OutputPath = filePath;

            await DownloadStreamAsync(selectedStream, filePath, item, _cancellationTokenSource!.Token);

            item.Status = DownloadStatus.Completed;
            item.EndTime = DateTime.Now;
            DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, true, null));
            return true;
        }

        /// <summary>
        /// تحميل فيديو عالي الجودة مع دمج الصوت (تحميل منفصل ثم دمج)
        /// </summary>
        private async Task<bool> DownloadVideoWithAudioAsync(DownloadItem item, string outputDirectory, string quality, StreamManifest streamManifest)
        {
            var qualityLabel = quality.Replace(" (فيديو + صوت)", "");

            // اختيار أفضل تدفق فيديو
            var videoStream = streamManifest.GetVideoOnlyStreams()
                .FirstOrDefault(s => s.VideoQuality.Label == qualityLabel);

            // اختيار أفضل تدفق صوت
            var audioStream = streamManifest.GetAudioOnlyStreams()
                .OrderByDescending(s => s.Bitrate)
                .FirstOrDefault();

            if (videoStream == null || audioStream == null)
            {
                throw new Exception("لم يتم العثور على تدفقات الفيديو والصوت المطلوبة");
            }

            StatusChanged?.Invoke(this, $"تحميل الفيديو والصوت منفصلين: {item.Title}");

            // إنشاء ملفات مؤقتة
            var tempVideoPath = Path.Combine(outputDirectory, $"temp_video_{Guid.NewGuid()}.{videoStream.Container.Name}");
            var tempAudioPath = Path.Combine(outputDirectory, $"temp_audio_{Guid.NewGuid()}.{audioStream.Container.Name}");
            var finalPath = Path.Combine(outputDirectory, $"{SanitizeFileName(item.Title)}.mp4");

            try
            {
                // تحميل الفيديو
                StatusChanged?.Invoke(this, $"تحميل الفيديو: {item.Title}");
                item.FileSize = (videoStream.Size.Bytes) + (audioStream.Size.Bytes);
                await DownloadStreamAsync(videoStream, tempVideoPath, item, _cancellationTokenSource!.Token);

                // تحميل الصوت
                StatusChanged?.Invoke(this, $"تحميل الصوت: {item.Title}");
                await DownloadStreamAsync(audioStream, tempAudioPath, item, _cancellationTokenSource!.Token);

                // دمج الفيديو والصوت
                StatusChanged?.Invoke(this, $"دمج الفيديو والصوت: {item.Title}");
                await MergeVideoAndAudioAsync(tempVideoPath, tempAudioPath, finalPath);

                item.OutputPath = finalPath;
                item.Status = DownloadStatus.Completed;
                item.EndTime = DateTime.Now;
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, true, null));

                return true;
            }
            finally
            {
                // حذف الملفات المؤقتة
                try
                {
                    if (File.Exists(tempVideoPath)) File.Delete(tempVideoPath);
                    if (File.Exists(tempAudioPath)) File.Delete(tempAudioPath);
                }
                catch
                {
                    // تجاهل أخطاء حذف الملفات المؤقتة
                }
            }
        }

        /// <summary>
        /// دمج ملفات الفيديو والصوت
        /// </summary>
        private async Task MergeVideoAndAudioAsync(string videoPath, string audioPath, string outputPath)
        {
            if (FFmpegHelper.IsFFmpegAvailable())
            {
                // استخدام FFmpeg للدمج الحقيقي
                var progress = new Progress<string>(message => StatusChanged?.Invoke(this, message));

                try
                {
                    await FFmpegHelper.MergeVideoAndAudioAsync(videoPath, audioPath, outputPath, progress);
                    StatusChanged?.Invoke(this, "تم دمج الفيديو والصوت بنجاح");
                }
                catch (Exception ex)
                {
                    StatusChanged?.Invoke(this, $"فشل في دمج الفيديو والصوت: {ex.Message}");

                    // في حالة فشل الدمج، احفظ الفيديو فقط
                    StatusChanged?.Invoke(this, "سيتم حفظ الفيديو فقط...");
                    await Task.Run(() => File.Copy(videoPath, outputPath, true));
                }
            }
            else
            {
                // FFmpeg غير متوفر - احفظ الفيديو فقط مع تنبيه
                StatusChanged?.Invoke(this, "تحذير: FFmpeg غير متوفر. سيتم حفظ الفيديو فقط.");
                StatusChanged?.Invoke(this, "لدمج الفيديو والصوت، يرجى تثبيت FFmpeg.");

                await Task.Run(() => File.Copy(videoPath, outputPath, true));

                // عرض تعليمات التثبيت
                var instructions = FFmpegHelper.GetInstallationInstructions();
                StatusChanged?.Invoke(this, "راجع التعليمات في الملف README.md لتثبيت FFmpeg");
            }
        }

        private async Task DownloadStreamAsync(IStreamInfo streamInfo, string filePath, DownloadItem item, CancellationToken cancellationToken)
        {
            using var response = await _httpClient.GetAsync(streamInfo.Url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            item.FileSize = response.Content.Headers.ContentLength ?? 0;

            using var contentStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

            var buffer = new byte[8192];
            var totalBytesRead = 0L;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            while (true)
            {
                var bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                if (bytesRead == 0) break;

                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                totalBytesRead += bytesRead;
                item.DownloadedBytes = totalBytesRead;

                // حساب السرعة والوقت المتبقي
                if (stopwatch.ElapsedMilliseconds > 1000) // تحديث كل ثانية
                {
                    item.DownloadSpeed = totalBytesRead / stopwatch.Elapsed.TotalSeconds;
                    if (item.FileSize > 0 && item.DownloadSpeed > 0)
                    {
                        var remainingBytes = item.FileSize - totalBytesRead;
                        item.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / item.DownloadSpeed);
                    }

                    ProgressChanged?.Invoke(this, new DownloadProgressEventArgs(item));
                }
            }
        }

        /// <summary>
        /// إلغاء التحميل الحالي
        /// </summary>
        public void CancelDownload()
        {
            _cancellationTokenSource?.Cancel();
        }

        private static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
            _httpClient?.Dispose();
        }
    }

    // فئات الأحداث
    public class DownloadProgressEventArgs : EventArgs
    {
        public DownloadItem Item { get; }

        public DownloadProgressEventArgs(DownloadItem item)
        {
            Item = item;
        }
    }

    public class DownloadCompletedEventArgs : EventArgs
    {
        public DownloadItem Item { get; }
        public bool Success { get; }
        public string? ErrorMessage { get; }

        public DownloadCompletedEventArgs(DownloadItem item, bool success, string? errorMessage)
        {
            Item = item;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }

    // فئة معلومات الفيديو
    public class VideoInfo
    {
        public string Title { get; set; } = string.Empty;
        public bool IsPlaylist { get; set; }
        public List<DownloadItem> Videos { get; set; } = new();
        public List<string> AvailableQualities { get; set; } = new();
        public List<string> AvailableFormats { get; set; } = new();
    }
}
