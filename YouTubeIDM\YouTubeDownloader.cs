using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using YoutubeExplode;
using YoutubeExplode.Videos;
using YoutubeExplode.Videos.Streams;
using YoutubeExplode.Playlists;
using YoutubeExplode.Common;

namespace YouTubeIDM
{
    /// <summary>
    /// فئة للتعامل مع عمليات تحميل الفيديوهات من YouTube
    /// </summary>
    public class YouTubeDownloader
    {
        private readonly YoutubeClient _youtube;
        private readonly HttpClient _httpClient;
        private CancellationTokenSource? _cancellationTokenSource;

        public event EventHandler<DownloadProgressEventArgs>? ProgressChanged;
        public event EventHandler<DownloadCompletedEventArgs>? DownloadCompleted;
        public event EventHandler<string>? StatusChanged;

        public YouTubeDownloader()
        {
            _youtube = new YoutubeClient();
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromMinutes(30); // مهلة زمنية طويلة للتحميلات الكبيرة
        }

        /// <summary>
        /// تحليل URL واستخراج معلومات الفيديو أو القائمة
        /// </summary>
        public async Task<VideoInfo?> GetVideoInfoAsync(string url)
        {
            try
            {
                StatusChanged?.Invoke(this, "جاري تحليل الرابط...");

                // التحقق من نوع الرابط
                if (PlaylistId.TryParse(url) != null)
                {
                    // هذا رابط قائمة تشغيل
                    return await GetPlaylistInfoAsync(url);
                }
                else if (VideoId.TryParse(url) != null)
                {
                    // هذا رابط فيديو واحد
                    return await GetSingleVideoInfoAsync(url);
                }
                else
                {
                    throw new ArgumentException("رابط غير صحيح. يرجى التأكد من أن الرابط من YouTube.");
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"خطأ في تحليل الرابط: {ex.Message}");
                return null;
            }
        }

        private async Task<VideoInfo> GetSingleVideoInfoAsync(string url)
        {
            var video = await _youtube.Videos.GetAsync(url);
            var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(video.Id);

            var videoInfo = new VideoInfo
            {
                Title = video.Title,
                IsPlaylist = false,
                Videos = new List<DownloadItem>()
            };

            var downloadItem = new DownloadItem
            {
                Id = video.Id,
                Title = video.Title,
                Url = url,
                ThumbnailUrl = video.Thumbnails.GetWithHighestResolution()?.Url ?? "",
                Duration = video.Duration ?? TimeSpan.Zero,
                Author = video.Author.ChannelTitle
            };

            videoInfo.Videos.Add(downloadItem);
            videoInfo.AvailableQualities = GetAvailableQualities(streamManifest);
            videoInfo.AvailableFormats = GetAvailableFormats(streamManifest);

            return videoInfo;
        }

        private async Task<VideoInfo> GetPlaylistInfoAsync(string url)
        {
            var playlist = await _youtube.Playlists.GetAsync(url);
            var videos = await _youtube.Playlists.GetVideosAsync(playlist.Id);

            var videoInfo = new VideoInfo
            {
                Title = playlist.Title,
                IsPlaylist = true,
                Videos = new List<DownloadItem>()
            };

            foreach (var video in videos)
            {
                var downloadItem = new DownloadItem
                {
                    Id = video.Id,
                    Title = video.Title,
                    Url = $"https://www.youtube.com/watch?v={video.Id}",
                    ThumbnailUrl = video.Thumbnails.GetWithHighestResolution()?.Url ?? "",
                    Duration = video.Duration ?? TimeSpan.Zero,
                    Author = video.Author.ChannelTitle
                };

                videoInfo.Videos.Add(downloadItem);
            }

            // الحصول على الجودات المتاحة من أول فيديو
            if (videoInfo.Videos.Any())
            {
                var firstVideo = videoInfo.Videos.First();
                var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(firstVideo.Id);
                videoInfo.AvailableQualities = GetAvailableQualities(streamManifest);
                videoInfo.AvailableFormats = GetAvailableFormats(streamManifest);
            }

            return videoInfo;
        }

        private List<string> GetAvailableQualities(StreamManifest streamManifest)
        {
            var qualities = new List<string>();

            // إضافة الجودات المدمجة (فيديو + صوت)
            var muxedStreams = streamManifest.GetMuxedStreams().OrderByDescending(s => s.VideoQuality.MaxHeight);
            foreach (var stream in muxedStreams)
            {
                qualities.Add($"{stream.VideoQuality.Label} (مدمج)");
            }

            // إضافة جودات الفيديو فقط
            var videoStreams = streamManifest.GetVideoOnlyStreams().OrderByDescending(s => s.VideoQuality.MaxHeight);
            foreach (var stream in videoStreams)
            {
                qualities.Add($"{stream.VideoQuality.Label} (فيديو فقط)");
            }

            // إضافة خيار الصوت فقط
            if (streamManifest.GetAudioOnlyStreams().Any())
            {
                qualities.Add("صوت فقط");
            }

            return qualities.Distinct().ToList();
        }

        private List<string> GetAvailableFormats(StreamManifest streamManifest)
        {
            var formats = new List<string>();

            // إضافة صيغ الصوت
            var audioStreams = streamManifest.GetAudioOnlyStreams();
            foreach (var stream in audioStreams)
            {
                var format = $"{stream.Container.Name.ToUpper()} ({stream.Bitrate})";
                if (!formats.Contains(format))
                    formats.Add(format);
            }

            return formats;
        }

        /// <summary>
        /// بدء تحميل فيديو
        /// </summary>
        public async Task<bool> DownloadVideoAsync(DownloadItem item, string outputDirectory, string quality, string format)
        {
            try
            {
                _cancellationTokenSource = new CancellationTokenSource();
                item.Status = DownloadStatus.Downloading;
                item.StartTime = DateTime.Now;

                StatusChanged?.Invoke(this, $"جاري تحميل: {item.Title}");

                var streamManifest = await _youtube.Videos.Streams.GetManifestAsync(item.Id);
                IStreamInfo? selectedStream = null;

                // اختيار التدفق المناسب حسب الجودة المحددة
                if (quality.Contains("صوت فقط"))
                {
                    selectedStream = streamManifest.GetAudioOnlyStreams()
                        .Where(s => format.Contains(s.Container.Name.ToUpper()))
                        .OrderByDescending(s => s.Bitrate)
                        .FirstOrDefault();
                }
                else if (quality.Contains("مدمج"))
                {
                    var qualityLabel = quality.Replace(" (مدمج)", "");
                    selectedStream = streamManifest.GetMuxedStreams()
                        .FirstOrDefault(s => s.VideoQuality.Label == qualityLabel);
                }
                else if (quality.Contains("فيديو فقط"))
                {
                    var qualityLabel = quality.Replace(" (فيديو فقط)", "");
                    selectedStream = streamManifest.GetVideoOnlyStreams()
                        .FirstOrDefault(s => s.VideoQuality.Label == qualityLabel);
                }

                if (selectedStream == null)
                {
                    throw new Exception("لم يتم العثور على التدفق المطلوب");
                }

                // تحديد اسم الملف ومساره
                var fileName = $"{SanitizeFileName(item.Title)}.{selectedStream.Container.Name}";
                var filePath = Path.Combine(outputDirectory, fileName);
                item.OutputPath = filePath;

                // تحميل الملف
                await DownloadStreamAsync(selectedStream, filePath, item, _cancellationTokenSource.Token);

                item.Status = DownloadStatus.Completed;
                item.EndTime = DateTime.Now;
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, true, null));

                return true;
            }
            catch (OperationCanceledException)
            {
                item.Status = DownloadStatus.Cancelled;
                StatusChanged?.Invoke(this, "تم إلغاء التحميل");
                return false;
            }
            catch (Exception ex)
            {
                item.Status = DownloadStatus.Failed;
                item.ErrorMessage = ex.Message;
                DownloadCompleted?.Invoke(this, new DownloadCompletedEventArgs(item, false, ex.Message));
                StatusChanged?.Invoke(this, $"فشل التحميل: {ex.Message}");
                return false;
            }
        }

        private async Task DownloadStreamAsync(IStreamInfo streamInfo, string filePath, DownloadItem item, CancellationToken cancellationToken)
        {
            using var response = await _httpClient.GetAsync(streamInfo.Url, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            item.FileSize = response.Content.Headers.ContentLength ?? 0;

            using var contentStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

            var buffer = new byte[8192];
            var totalBytesRead = 0L;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            while (true)
            {
                var bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
                if (bytesRead == 0) break;

                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                totalBytesRead += bytesRead;
                item.DownloadedBytes = totalBytesRead;

                // حساب السرعة والوقت المتبقي
                if (stopwatch.ElapsedMilliseconds > 1000) // تحديث كل ثانية
                {
                    item.DownloadSpeed = totalBytesRead / stopwatch.Elapsed.TotalSeconds;
                    if (item.FileSize > 0 && item.DownloadSpeed > 0)
                    {
                        var remainingBytes = item.FileSize - totalBytesRead;
                        item.EstimatedTimeRemaining = TimeSpan.FromSeconds(remainingBytes / item.DownloadSpeed);
                    }

                    ProgressChanged?.Invoke(this, new DownloadProgressEventArgs(item));
                }
            }
        }

        /// <summary>
        /// إلغاء التحميل الحالي
        /// </summary>
        public void CancelDownload()
        {
            _cancellationTokenSource?.Cancel();
        }

        private static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        public void Dispose()
        {
            _cancellationTokenSource?.Dispose();
            _httpClient?.Dispose();
        }
    }

    // فئات الأحداث
    public class DownloadProgressEventArgs : EventArgs
    {
        public DownloadItem Item { get; }

        public DownloadProgressEventArgs(DownloadItem item)
        {
            Item = item;
        }
    }

    public class DownloadCompletedEventArgs : EventArgs
    {
        public DownloadItem Item { get; }
        public bool Success { get; }
        public string? ErrorMessage { get; }

        public DownloadCompletedEventArgs(DownloadItem item, bool success, string? errorMessage)
        {
            Item = item;
            Success = success;
            ErrorMessage = errorMessage;
        }
    }

    // فئة معلومات الفيديو
    public class VideoInfo
    {
        public string Title { get; set; } = string.Empty;
        public bool IsPlaylist { get; set; }
        public List<DownloadItem> Videos { get; set; } = new();
        public List<string> AvailableQualities { get; set; } = new();
        public List<string> AvailableFormats { get; set; } = new();
    }
}
