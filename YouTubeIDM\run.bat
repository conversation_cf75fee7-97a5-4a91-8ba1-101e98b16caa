@echo off
echo Starting YouTube IDM...
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: .NET is not installed or not found in PATH
    echo Please install .NET 9.0 or later from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

REM Build and run the application
echo Building application...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo Error: Build failed
    pause
    exit /b 1
)

echo.
echo Starting YouTube IDM...
dotnet run --configuration Release

pause
