{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"YouTubeIDM/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "YoutubeExplode": "6.5.4"}, "runtime": {"YouTubeIDM.dll": {}}}, "AngleSharp/1.2.0": {"runtime": {"lib/net8.0/AngleSharp.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "YoutubeExplode/6.5.4": {"dependencies": {"AngleSharp": "1.2.0"}, "runtime": {"lib/net9.0/YoutubeExplode.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"YouTubeIDM/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-uF/PzSCVcb+b2nqVvHZbOqexoJ9R6QLjonugPf0PQl+0h7YKaFZeXyspctbHe5HGlx7/Iuk5BErtk+t63ac/ZA==", "path": "anglesharp/1.2.0", "hashPath": "anglesharp.1.2.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "YoutubeExplode/6.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-SWMvvvC/xEjkDEnQW28xeBXn0mgB7tsNtXvtcMpxYioyVuUWzsx94oWVQgUZIaT+Zbmvj03LaKIwK9mTGKMZQg==", "path": "youtubeexplode/6.5.4", "hashPath": "youtubeexplode.6.5.4.nupkg.sha512"}}}