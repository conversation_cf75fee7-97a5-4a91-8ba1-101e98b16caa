using System;
using System.IO;
using Newtonsoft.Json;

namespace YouTubeIDM
{
    /// <summary>
    /// فئة لإدارة إعدادات التطبيق
    /// </summary>
    public class AppSettings
    {
        private static readonly string SettingsPath = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "YouTubeIDM",
            "settings.json"
        );

        public string DefaultDownloadPath { get; set; } = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        public string DefaultVideoQuality { get; set; } = "720p (مدمج)";
        public string DefaultAudioFormat { get; set; } = "MP4";
        public bool RememberWindowSize { get; set; } = true;
        public int WindowWidth { get; set; } = 760;
        public int WindowHeight { get; set; } = 680;
        public int WindowX { get; set; } = -1;
        public int WindowY { get; set; } = -1;
        public bool AutoSelectAllPlaylistVideos { get; set; } = true;
        public int MaxConcurrentDownloads { get; set; } = 1;
        public bool ShowDownloadCompleteNotification { get; set; } = true;
        public bool AutoOpenDownloadFolder { get; set; } = false;

        /// <summary>
        /// تحميل الإعدادات من الملف
        /// </summary>
        public static AppSettings Load()
        {
            try
            {
                if (File.Exists(SettingsPath))
                {
                    var json = File.ReadAllText(SettingsPath);
                    var settings = JsonConvert.DeserializeObject<AppSettings>(json);
                    return settings ?? new AppSettings();
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل تحميل الإعدادات، استخدم الإعدادات الافتراضية
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}");
            }

            return new AppSettings();
        }

        /// <summary>
        /// حفظ الإعدادات إلى الملف
        /// </summary>
        public void Save()
        {
            try
            {
                var directory = Path.GetDirectoryName(SettingsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(SettingsPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ الإعدادات: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات إلى القيم الافتراضية
        /// </summary>
        public void Reset()
        {
            DefaultDownloadPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            DefaultVideoQuality = "720p (مدمج)";
            DefaultAudioFormat = "MP4";
            RememberWindowSize = true;
            WindowWidth = 760;
            WindowHeight = 680;
            WindowX = -1;
            WindowY = -1;
            AutoSelectAllPlaylistVideos = true;
            MaxConcurrentDownloads = 1;
            ShowDownloadCompleteNotification = true;
            AutoOpenDownloadFolder = false;
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        public void Validate()
        {
            // التحقق من مجلد التحميل
            if (string.IsNullOrWhiteSpace(DefaultDownloadPath) || !Directory.Exists(DefaultDownloadPath))
            {
                DefaultDownloadPath = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }

            // التحقق من أبعاد النافذة
            if (WindowWidth < 760) WindowWidth = 760;
            if (WindowHeight < 680) WindowHeight = 680;

            // التحقق من عدد التحميلات المتزامنة
            if (MaxConcurrentDownloads < 1) MaxConcurrentDownloads = 1;
            if (MaxConcurrentDownloads > 5) MaxConcurrentDownloads = 5;
        }
    }
}
